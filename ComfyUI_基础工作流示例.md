# ComfyUI 基础工作流示例

## 工作流1：基础文生图 (Text-to-Image)

### 节点配置
```json
{
  "workflow_name": "basic_txt2img",
  "description": "最基础的文生图工作流",
  "nodes": {
    "1": {
      "type": "CheckpointLoaderSimple",
      "inputs": {
        "ckpt_name": "v1-5-pruned-emaonly.ckpt"
      },
      "outputs": ["MODEL", "CLIP", "VAE"]
    },
    "2": {
      "type": "CLIPTextEncode",
      "inputs": {
        "text": "a beautiful cat, high quality, detailed",
        "clip": "1.CLIP"
      },
      "outputs": ["CONDITIONING"]
    },
    "3": {
      "type": "CLIPTextEncode", 
      "inputs": {
        "text": "blurry, low quality, bad anatomy",
        "clip": "1.CLIP"
      },
      "outputs": ["CONDITIONING"]
    },
    "4": {
      "type": "EmptyLatentImage",
      "inputs": {
        "width": 512,
        "height": 512,
        "batch_size": 1
      },
      "outputs": ["LATENT"]
    },
    "5": {
      "type": "KSampler",
      "inputs": {
        "seed": 42,
        "steps": 20,
        "cfg": 7.0,
        "sampler_name": "dpmpp_2m",
        "scheduler": "karras",
        "denoise": 1.0,
        "model": "1.MODEL",
        "positive": "2.CONDITIONING",
        "negative": "3.CONDITIONING",
        "latent_image": "4.LATENT"
      },
      "outputs": ["LATENT"]
    },
    "6": {
      "type": "VAEDecode",
      "inputs": {
        "samples": "5.LATENT",
        "vae": "1.VAE"
      },
      "outputs": ["IMAGE"]
    },
    "7": {
      "type": "SaveImage",
      "inputs": {
        "images": "6.IMAGE",
        "filename_prefix": "ComfyUI_basic"
      }
    }
  }
}
```

### Python代码实现
```python
# basic_txt2img.py
import json
import requests

def create_basic_txt2img_workflow(
    positive_prompt="a beautiful cat, high quality, detailed",
    negative_prompt="blurry, low quality, bad anatomy",
    width=512,
    height=512,
    steps=20,
    cfg=7.0,
    seed=42
):
    """创建基础文生图工作流"""
    
    workflow = {
        "1": {
            "inputs": {
                "ckpt_name": "v1-5-pruned-emaonly.ckpt"
            },
            "class_type": "CheckpointLoaderSimple"
        },
        "2": {
            "inputs": {
                "text": positive_prompt,
                "clip": ["1", 1]
            },
            "class_type": "CLIPTextEncode"
        },
        "3": {
            "inputs": {
                "text": negative_prompt,
                "clip": ["1", 1]
            },
            "class_type": "CLIPTextEncode"
        },
        "4": {
            "inputs": {
                "width": width,
                "height": height,
                "batch_size": 1
            },
            "class_type": "EmptyLatentImage"
        },
        "5": {
            "inputs": {
                "seed": seed,
                "steps": steps,
                "cfg": cfg,
                "sampler_name": "dpmpp_2m",
                "scheduler": "karras",
                "denoise": 1.0,
                "model": ["1", 0],
                "positive": ["2", 0],
                "negative": ["3", 0],
                "latent_image": ["4", 0]
            },
            "class_type": "KSampler"
        },
        "6": {
            "inputs": {
                "samples": ["5", 0],
                "vae": ["1", 2]
            },
            "class_type": "VAEDecode"
        },
        "7": {
            "inputs": {
                "images": ["6", 0],
                "filename_prefix": "ComfyUI_basic"
            },
            "class_type": "SaveImage"
        }
    }
    
    return workflow

def submit_workflow(workflow, server_url="http://localhost:8188"):
    """提交工作流到ComfyUI"""
    
    response = requests.post(
        f"{server_url}/prompt",
        json={"prompt": workflow}
    )
    
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Failed to submit workflow: {response.text}")

# 使用示例
if __name__ == "__main__":
    workflow = create_basic_txt2img_workflow(
        positive_prompt="a cute dog playing in the park",
        negative_prompt="blurry, low quality",
        steps=25,
        cfg=8.0
    )
    
    result = submit_workflow(workflow)
    print(f"Workflow submitted with ID: {result['prompt_id']}")
```

## 工作流2：图生图 (Image-to-Image)

### 节点配置
```python
def create_img2img_workflow(
    input_image_path,
    positive_prompt,
    negative_prompt="blurry, low quality",
    denoise_strength=0.7,
    steps=20,
    cfg=7.0,
    seed=42
):
    """创建图生图工作流"""
    
    workflow = {
        "1": {
            "inputs": {
                "ckpt_name": "v1-5-pruned-emaonly.ckpt"
            },
            "class_type": "CheckpointLoaderSimple"
        },
        "2": {
            "inputs": {
                "text": positive_prompt,
                "clip": ["1", 1]
            },
            "class_type": "CLIPTextEncode"
        },
        "3": {
            "inputs": {
                "text": negative_prompt,
                "clip": ["1", 1]
            },
            "class_type": "CLIPTextEncode"
        },
        "4": {
            "inputs": {
                "image": input_image_path
            },
            "class_type": "LoadImage"
        },
        "5": {
            "inputs": {
                "pixels": ["4", 0],
                "vae": ["1", 2]
            },
            "class_type": "VAEEncode"
        },
        "6": {
            "inputs": {
                "seed": seed,
                "steps": steps,
                "cfg": cfg,
                "sampler_name": "dpmpp_2m",
                "scheduler": "karras",
                "denoise": denoise_strength,
                "model": ["1", 0],
                "positive": ["2", 0],
                "negative": ["3", 0],
                "latent_image": ["5", 0]
            },
            "class_type": "KSampler"
        },
        "7": {
            "inputs": {
                "samples": ["6", 0],
                "vae": ["1", 2]
            },
            "class_type": "VAEDecode"
        },
        "8": {
            "inputs": {
                "images": ["7", 0],
                "filename_prefix": "ComfyUI_img2img"
            },
            "class_type": "SaveImage"
        }
    }
    
    return workflow
```

## 工作流3：ControlNet工作流

### Canny边缘检测工作流
```python
def create_controlnet_canny_workflow(
    input_image_path,
    positive_prompt,
    negative_prompt="blurry, low quality",
    controlnet_strength=1.0,
    steps=20,
    cfg=7.0,
    seed=42
):
    """创建ControlNet Canny工作流"""
    
    workflow = {
        "1": {
            "inputs": {
                "ckpt_name": "v1-5-pruned-emaonly.ckpt"
            },
            "class_type": "CheckpointLoaderSimple"
        },
        "2": {
            "inputs": {
                "text": positive_prompt,
                "clip": ["1", 1]
            },
            "class_type": "CLIPTextEncode"
        },
        "3": {
            "inputs": {
                "text": negative_prompt,
                "clip": ["1", 1]
            },
            "class_type": "CLIPTextEncode"
        },
        "4": {
            "inputs": {
                "image": input_image_path
            },
            "class_type": "LoadImage"
        },
        "5": {
            "inputs": {
                "control_net_name": "control_v11p_sd15_canny.pth"
            },
            "class_type": "ControlNetLoader"
        },
        "6": {
            "inputs": {
                "image": ["4", 0],
                "low_threshold": 100,
                "high_threshold": 200
            },
            "class_type": "CannyEdgePreprocessor"
        },
        "7": {
            "inputs": {
                "conditioning": ["2", 0],
                "control_net": ["5", 0],
                "image": ["6", 0],
                "strength": controlnet_strength
            },
            "class_type": "ControlNetApply"
        },
        "8": {
            "inputs": {
                "width": 512,
                "height": 512,
                "batch_size": 1
            },
            "class_type": "EmptyLatentImage"
        },
        "9": {
            "inputs": {
                "seed": seed,
                "steps": steps,
                "cfg": cfg,
                "sampler_name": "dpmpp_2m",
                "scheduler": "karras",
                "denoise": 1.0,
                "model": ["1", 0],
                "positive": ["7", 0],
                "negative": ["3", 0],
                "latent_image": ["8", 0]
            },
            "class_type": "KSampler"
        },
        "10": {
            "inputs": {
                "samples": ["9", 0],
                "vae": ["1", 2]
            },
            "class_type": "VAEDecode"
        },
        "11": {
            "inputs": {
                "images": ["10", 0],
                "filename_prefix": "ComfyUI_controlnet"
            },
            "class_type": "SaveImage"
        }
    }
    
    return workflow
```

## 工作流4：LoRA应用工作流

```python
def create_lora_workflow(
    positive_prompt,
    negative_prompt="blurry, low quality",
    lora_name="style_lora.safetensors",
    lora_strength=0.8,
    width=512,
    height=512,
    steps=20,
    cfg=7.0,
    seed=42
):
    """创建LoRA应用工作流"""
    
    workflow = {
        "1": {
            "inputs": {
                "ckpt_name": "v1-5-pruned-emaonly.ckpt"
            },
            "class_type": "CheckpointLoaderSimple"
        },
        "2": {
            "inputs": {
                "lora_name": lora_name,
                "strength_model": lora_strength,
                "strength_clip": lora_strength,
                "model": ["1", 0],
                "clip": ["1", 1]
            },
            "class_type": "LoraLoader"
        },
        "3": {
            "inputs": {
                "text": positive_prompt,
                "clip": ["2", 1]
            },
            "class_type": "CLIPTextEncode"
        },
        "4": {
            "inputs": {
                "text": negative_prompt,
                "clip": ["2", 1]
            },
            "class_type": "CLIPTextEncode"
        },
        "5": {
            "inputs": {
                "width": width,
                "height": height,
                "batch_size": 1
            },
            "class_type": "EmptyLatentImage"
        },
        "6": {
            "inputs": {
                "seed": seed,
                "steps": steps,
                "cfg": cfg,
                "sampler_name": "dpmpp_2m",
                "scheduler": "karras",
                "denoise": 1.0,
                "model": ["2", 0],
                "positive": ["3", 0],
                "negative": ["4", 0],
                "latent_image": ["5", 0]
            },
            "class_type": "KSampler"
        },
        "7": {
            "inputs": {
                "samples": ["6", 0],
                "vae": ["1", 2]
            },
            "class_type": "VAEDecode"
        },
        "8": {
            "inputs": {
                "images": ["7", 0],
                "filename_prefix": "ComfyUI_lora"
            },
            "class_type": "SaveImage"
        }
    }
    
    return workflow
```

## 工作流5：批量生成工作流

```python
def create_batch_workflow(
    prompts_list,
    negative_prompt="blurry, low quality",
    width=512,
    height=512,
    steps=20,
    cfg=7.0,
    base_seed=42
):
    """创建批量生成工作流"""
    
    workflows = []
    
    for i, prompt in enumerate(prompts_list):
        workflow = create_basic_txt2img_workflow(
            positive_prompt=prompt,
            negative_prompt=negative_prompt,
            width=width,
            height=height,
            steps=steps,
            cfg=cfg,
            seed=base_seed + i
        )
        
        # 修改输出文件名
        workflow["7"]["inputs"]["filename_prefix"] = f"batch_{i:03d}"
        
        workflows.append(workflow)
    
    return workflows

def submit_batch_workflows(workflows, server_url="http://localhost:8188"):
    """批量提交工作流"""
    
    results = []
    
    for i, workflow in enumerate(workflows):
        print(f"Submitting workflow {i+1}/{len(workflows)}")
        
        try:
            result = submit_workflow(workflow, server_url)
            results.append(result)
            print(f"✓ Workflow {i+1} submitted: {result['prompt_id']}")
        except Exception as e:
            print(f"✗ Workflow {i+1} failed: {e}")
            results.append(None)
    
    return results

# 使用示例
if __name__ == "__main__":
    prompts = [
        "a cat in a garden",
        "a dog on the beach", 
        "a bird in the sky",
        "a fish in the ocean"
    ]
    
    workflows = create_batch_workflow(prompts)
    results = submit_batch_workflows(workflows)
    
    print(f"Submitted {len([r for r in results if r])} workflows successfully")
```

## 工作流管理器

```python
class ComfyUIWorkflowManager:
    """ComfyUI工作流管理器"""
    
    def __init__(self, server_url="http://localhost:8188"):
        self.server_url = server_url
        self.workflows = {}
    
    def register_workflow(self, name, workflow_func):
        """注册工作流模板"""
        self.workflows[name] = workflow_func
    
    def create_workflow(self, name, **kwargs):
        """创建工作流实例"""
        if name not in self.workflows:
            raise ValueError(f"Unknown workflow: {name}")
        
        return self.workflows[name](**kwargs)
    
    def submit_workflow(self, workflow):
        """提交工作流"""
        return submit_workflow(workflow, self.server_url)
    
    def list_workflows(self):
        """列出所有注册的工作流"""
        return list(self.workflows.keys())

# 使用示例
manager = ComfyUIWorkflowManager()

# 注册工作流模板
manager.register_workflow("txt2img", create_basic_txt2img_workflow)
manager.register_workflow("img2img", create_img2img_workflow)
manager.register_workflow("controlnet", create_controlnet_canny_workflow)
manager.register_workflow("lora", create_lora_workflow)

# 使用工作流
workflow = manager.create_workflow(
    "txt2img",
    positive_prompt="a beautiful landscape",
    steps=25,
    cfg=8.0
)

result = manager.submit_workflow(workflow)
print(f"Workflow submitted: {result['prompt_id']}")
```

## 调试和监控

```python
import time
import websocket
import json
import threading

class ComfyUIMonitor:
    """ComfyUI执行监控器"""
    
    def __init__(self, server_url="http://localhost:8188"):
        self.server_url = server_url
        self.ws_url = server_url.replace("http", "ws") + "/ws"
        self.running = False
        self.callbacks = {}
    
    def on_message(self, ws, message):
        """处理WebSocket消息"""
        try:
            data = json.loads(message)
            msg_type = data.get("type")
            
            if msg_type in self.callbacks:
                self.callbacks[msg_type](data)
            
            # 默认处理
            if msg_type == "executing":
                node_id = data["data"]["node"]
                if node_id:
                    print(f"Executing node: {node_id}")
                else:
                    print("Execution completed")
            
            elif msg_type == "progress":
                value = data["data"]["value"]
                max_value = data["data"]["max"]
                print(f"Progress: {value}/{max_value}")
            
            elif msg_type == "executed":
                node_id = data["data"]["node"]
                print(f"Node {node_id} completed")
        
        except Exception as e:
            print(f"Error processing message: {e}")
    
    def on_error(self, ws, error):
        print(f"WebSocket error: {error}")
    
    def on_close(self, ws, close_status_code, close_msg):
        print("WebSocket connection closed")
        self.running = False
    
    def on_open(self, ws):
        print("WebSocket connection opened")
        self.running = True
    
    def start_monitoring(self):
        """开始监控"""
        self.ws = websocket.WebSocketApp(
            self.ws_url,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close,
            on_open=self.on_open
        )
        
        # 在单独线程中运行
        self.monitor_thread = threading.Thread(
            target=self.ws.run_forever
        )
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        if hasattr(self, 'ws'):
            self.ws.close()
        self.running = False
    
    def register_callback(self, msg_type, callback):
        """注册消息回调"""
        self.callbacks[msg_type] = callback

# 使用示例
monitor = ComfyUIMonitor()

def on_execution_complete(data):
    print("🎉 Generation completed!")

monitor.register_callback("executed", on_execution_complete)
monitor.start_monitoring()

# 提交工作流
workflow = create_basic_txt2img_workflow()
result = submit_workflow(workflow)

# 等待完成
time.sleep(30)
monitor.stop_monitoring()
```

这些示例展示了ComfyUI的核心工作流模式。作为程序员，你可以：

1. **模块化设计**：将工作流封装为可复用的函数
2. **参数化配置**：通过参数控制工作流行为
3. **批量处理**：实现高效的批量生成
4. **监控调试**：实时监控执行状态
5. **API集成**：将ComfyUI集成到你的应用中

建议从基础工作流开始，逐步掌握更复杂的功能。
