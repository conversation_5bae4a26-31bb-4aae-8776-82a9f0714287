# Stable Diffusion 快速参考卡片

## 🚀 快速开始

### 基础操作流程
1. **启动WebUI** → 访问 `http://localhost:7860`
2. **输入提示词** → 描述你想要的图片
3. **设置参数** → 采样器、步数、CFG
4. **点击生成** → 等待结果
5. **调整优化** → 根据结果调整提示词和参数

### 新手推荐设置
```
采样器：DPM++ 2M Karras
步数：20-25
CFG Scale：7-10
尺寸：512x512 (SD1.5) 或 1024x1024 (SDXL)
```

## 📝 提示词速查

### 基础结构
```
[主体] + [描述] + [环境] + [风格] + [质量词]

示例：
beautiful girl, long hair, blue eyes, garden background, 
anime style, high quality, detailed
```

### 常用质量词
```
正向：high quality, detailed, masterpiece, best quality, 
      sharp, clear, professional, 4k, ultra detailed

负向：low quality, blurry, bad anatomy, distorted, ugly,
      bad hands, watermark, text, cropped
```

### 权重语法
```
(keyword)      # 1.1倍权重
((keyword))    # 1.21倍权重  
(keyword:1.5)  # 1.5倍权重
[keyword]      # 0.9倍权重
```

## ⚙️ 参数速查

### 采样器选择
```
快速预览：Euler a
日常使用：DPM++ 2M Karras  
高质量：DDIM
实验性：DPM++ SDE Karras
```

### 步数指南
```
15-20步：快速预览
20-30步：日常使用 ⭐
30-50步：高质量输出
50+步：通常无明显改善
```

### CFG Scale指南
```
1-5：AI自由发挥
7-12：平衡推荐 ⭐
15+：严格遵循提示词
```

### 尺寸建议
```
SD 1.5：
- 512x512 (标准)
- 512x768 (竖图)
- 768x512 (横图)

SDXL：
- 1024x1024 (标准)
- 1024x1536 (竖图)  
- 1536x1024 (横图)
```

## 🎨 风格速查

### 艺术风格
```
写实：photorealistic, realistic, photography
动漫：anime, manga, cartoon, kawaii
绘画：oil painting, watercolor, digital art
素描：pencil drawing, sketch, line art
```

### 光线效果
```
自然光：natural lighting, sunlight, golden hour
人工光：studio lighting, neon lights, candlelight
特殊光：volumetric lighting, rim lighting, backlighting
```

### 构图术语
```
视角：close-up, medium shot, wide shot, bird's eye view
角度：front view, side view, three-quarter view
景深：shallow depth of field, bokeh, sharp focus
```

## 🔧 常用功能

### 图生图参数
```
Denoising strength：
0.1-0.3：轻微修改
0.4-0.7：中等修改 ⭐
0.8-1.0：大幅重绘
```

### LoRA使用
```
语法：<lora:模型名:权重>
权重：0.5-1.2 (推荐0.8)
示例：<lora:korean_doll:0.8>
```

### 批量生成
```
Batch count：生成批次数
Batch size：每批图片数
建议：Batch count > Batch size (节省显存)
```

## 🛠️ 故障排除

### 显存不足
```
解决方案：
1. 添加启动参数：--medvram 或 --lowvram
2. 降低图片分辨率
3. 减少批量生成数量
4. 关闭其他程序
```

### 生成质量差
```
检查项目：
1. 提示词是否清晰具体
2. 负向提示词是否完整
3. 模型是否适合当前风格
4. 参数设置是否合理
```

### 生成速度慢
```
优化方法：
1. 添加 --xformers 参数
2. 使用更快的采样器
3. 减少采样步数
4. 降低图片分辨率
```

## 📚 学习资源

### 官方资源
- **WebUI GitHub**：https://github.com/AUTOMATIC1111/stable-diffusion-webui
- **Hugging Face**：https://huggingface.co/spaces/stabilityai/stable-diffusion

### 社区资源  
- **Civitai**：模型下载和分享
- **OpenArt**：提示词学习
- **Reddit r/StableDiffusion**：社区讨论

### 中文资源
- **B站**：搜索"Stable Diffusion教程"
- **知乎**：技术文章和经验分享

## 🎯 实用技巧

### 提示词技巧
```
1. 从简单开始，逐步添加细节
2. 使用具体而非抽象的描述
3. 参考优秀作品的提示词
4. 建立个人提示词库
```

### 参数调优
```
1. 先固定大部分参数，只调整一个
2. 使用X/Y Plot批量测试
3. 记录有效的参数组合
4. 不同模型可能需要不同参数
```

### 模型选择
```
写实人像：Realistic Vision, ChilloutMix
动漫风格：Anything V5, CounterfeitV3  
通用创作：Deliberate, DreamShaper
艺术风格：根据具体需求选择
```

## 🔥 常用提示词模板

### 人物肖像
```
模板：
[年龄] [性别], [外貌特征], [表情], [服装], 
[姿势], [背景], [风格], [质量词]

示例：
young woman, beautiful face, gentle smile, white dress,
sitting pose, garden background, photorealistic, high quality
```

### 风景画
```
模板：
[主要景物] in [环境], [天气/时间], [风格], [质量词]

示例：
mountain lake in valley, golden hour sunset, 
oil painting style, masterpiece, detailed
```

### 动漫角色
```
模板：
[角色描述], [特殊特征], [服装], [背景], 
anime style, [质量词]

示例：
cute anime girl, cat ears, school uniform, classroom,
anime style, kawaii, high quality, detailed
```

## ⚡ 快捷键

### WebUI界面
```
Ctrl + Enter：生成图片
Ctrl + Z：撤销操作
Tab：在输入框间切换
Esc：取消当前操作
```

### 图片操作
```
点击图片：放大查看
右键图片：保存或发送到其他功能
拖拽图片：移动到其他区域
```

## 📊 性能参考

### 显卡性能对比
```
RTX 4090：1024x1024, 20步, ~10秒
RTX 3080：512x512, 20步, ~15秒  
RTX 3060：512x512, 20步, ~30秒
GTX 1660：512x512, 20步, ~60秒
```

### 内存使用
```
SD 1.5：~4GB 显存
SDXL：~8GB 显存
ControlNet：+2GB 显存
多个LoRA：+1GB 显存
```

---

**💡 小贴士**：
- 保存这个参考卡片，随时查阅
- 定期更新你的个人经验
- 多实践，多总结，多分享
- 加入社区，与他人交流学习

**🎨 记住**：好的AI绘画作品来自于：
`清晰的想法 + 准确的提示词 + 合适的参数 + 大量的练习`
