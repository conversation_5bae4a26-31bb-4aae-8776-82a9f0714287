# Python 3.10.6 安装指南

## 当前情况
- 你的系统安装了Python 3.13.5
- 需要替换为Python 3.10.6以兼容Stable Diffusion
- Chocolatey安装遇到权限问题

## 推荐解决方案：官网直接安装

### 步骤1：卸载Python 3.13

#### 方法A：通过设置卸载（推荐）
1. 按 `Win + I` 打开设置
2. 点击"应用"
3. 点击"应用和功能"
4. 在搜索框输入"Python"
5. 找到所有Python 3.13相关程序，逐一卸载：
   - Python 3.13.5 (64-bit)
   - Python Launcher
   - 其他Python 3.13组件

#### 方法B：通过控制面板卸载
1. 按 `Win + R`，输入 `appwiz.cpl`，回车
2. 找到所有Python 3.13相关程序
3. 右键选择"卸载"

### 步骤2：下载Python 3.10.6

1. **访问官网**：
   ```
   https://www.python.org/downloads/release/python-3106/
   ```

2. **选择正确的安装包**：
   - 下载 "Windows installer (64-bit)"
   - 文件名：python-3.10.6-amd64.exe
   - 大小：约28MB

### 步骤3：安装Python 3.10.6

1. **运行安装程序**：
   - 双击下载的 `python-3.10.6-amd64.exe`

2. **重要设置**：
   - ✅ **勾选 "Add Python 3.10 to PATH"**（非常重要！）
   - ✅ 勾选 "Install launcher for all users"
   - 点击 "Customize installation"

3. **可选功能**：
   - ✅ Documentation
   - ✅ pip
   - ✅ tcl/tk and IDLE
   - ✅ Python test suite
   - ✅ py launcher
   - ✅ for all users

4. **高级选项**：
   - ✅ Install for all users
   - ✅ Associate files with Python
   - ✅ Create shortcuts for installed applications
   - ✅ Add Python to environment variables
   - ✅ Precompile standard library
   - 安装路径：保持默认 `C:\Program Files\Python310\`

5. **完成安装**：
   - 点击 "Install"
   - 等待安装完成
   - 点击 "Close"

### 步骤4：验证安装

打开新的命令提示符或PowerShell窗口，运行：

```bash
python --version
```

应该显示：`Python 3.10.6`

```bash
pip --version
```

应该显示pip版本信息

### 步骤5：清理环境变量（如果需要）

如果仍然有Python 3.13的残留：

1. 按 `Win + R`，输入 `sysdm.cpl`，回车
2. 点击"高级"选项卡
3. 点击"环境变量"
4. 在"系统变量"中找到"Path"
5. 删除所有Python 3.13相关的路径
6. 确保Python 3.10的路径存在：
   - `C:\Program Files\Python310\`
   - `C:\Program Files\Python310\Scripts\`

## 备用方案：使用pyenv-win

如果你想要更灵活的Python版本管理：

### 安装pyenv-win
```bash
# 使用PowerShell（管理员权限）
Invoke-WebRequest -UseBasicParsing -Uri "https://raw.githubusercontent.com/pyenv-win/pyenv-win/master/pyenv-win/install-pyenv-win.ps1" -OutFile "./install-pyenv-win.ps1"; &"./install-pyenv-win.ps1"
```

### 使用pyenv安装Python 3.10.6
```bash
# 重启终端后
pyenv install 3.10.6
pyenv global 3.10.6
```

## 验证Stable Diffusion兼容性

安装完成后，测试是否可以安装Stable Diffusion依赖：

```bash
# 测试torch安装
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 如果成功，说明Python 3.10.6安装正确
python -c "import torch; print(torch.__version__)"
```

## 常见问题解决

### 问题1：PATH环境变量冲突
**症状**：运行python仍然显示3.13版本
**解决**：
1. 重启计算机
2. 检查环境变量PATH顺序
3. 确保Python 3.10路径在前面

### 问题2：pip版本冲突
**症状**：pip安装包到错误的Python版本
**解决**：
```bash
# 使用完整路径
C:\Program Files\Python310\python.exe -m pip install package_name
```

### 问题3：权限问题
**症状**：安装时提示权限不足
**解决**：
1. 右键安装程序，选择"以管理员身份运行"
2. 或者安装到用户目录而不是系统目录

## 安装后的下一步

1. **更新pip**：
   ```bash
   python -m pip install --upgrade pip
   ```

2. **安装Git**（如果还没有）：
   ```bash
   choco install git -y
   ```

3. **继续Stable Diffusion安装**：
   - 返回主教程的"安装配置指南.md"
   - 从"下载WebUI"步骤继续

## 检查清单

安装完成后，确认以下项目：

- [ ] `python --version` 显示 `Python 3.10.6`
- [ ] `pip --version` 正常显示版本信息
- [ ] 环境变量PATH包含Python 3.10路径
- [ ] 没有Python 3.13的残留文件
- [ ] 可以正常安装Python包

完成这些步骤后，你就可以继续安装Stable Diffusion了！
