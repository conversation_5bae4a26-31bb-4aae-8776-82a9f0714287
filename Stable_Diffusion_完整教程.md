# Stable Diffusion 完整入门教程

## 目录
1. [什么是Stable Diffusion](#什么是stable-diffusion)
2. [环境准备与安装](#环境准备与安装)
3. [基础概念](#基础概念)
4. [第一次生成图片](#第一次生成图片)
5. [提示词(Prompt)详解](#提示词prompt详解)
6. [参数调节](#参数调节)
7. [模型选择](#模型选择)
8. [进阶技巧](#进阶技巧)
9. [常见问题解决](#常见问题解决)
10. [学习资源](#学习资源)

## 什么是Stable Diffusion

Stable Diffusion是一个开源的AI图像生成模型，它可以根据文本描述生成高质量的图像。与其他AI绘画工具相比，它有以下特点：

- **开源免费**：完全开源，可以本地运行
- **硬件要求相对较低**：4GB显存即可运行基础版本
- **高度可定制**：支持各种插件和自定义模型
- **社区活跃**：有大量的模型、插件和教程

## 环境准备与安装

### 硬件要求
- **推荐配置**：
  - GPU：NVIDIA RTX 3060 或更高（8GB+ 显存）
  - CPU：Intel i5 或 AMD Ryzen 5 以上
  - 内存：16GB RAM
  - 存储：50GB+ 可用空间

- **最低配置**：
  - GPU：NVIDIA GTX 1060（6GB 显存）
  - CPU：Intel i3 或 AMD Ryzen 3
  - 内存：8GB RAM
  - 存储：20GB+ 可用空间

### 安装方式选择

#### 方式一：AUTOMATIC1111 WebUI（推荐新手）
这是最受欢迎的Stable Diffusion界面，功能强大且易于使用。

**安装步骤：**
1. 安装Python 3.10.6
2. 安装Git
3. 下载AUTOMATIC1111
4. 运行安装脚本

#### 方式二：ComfyUI（适合进阶用户）
节点式界面，更适合复杂的工作流程。

#### 方式三：在线平台
- Hugging Face Spaces
- Google Colab
- 各种在线AI绘画网站

## 基础概念

### 核心概念解释

1. **模型(Model)**
   - 基础模型：如SD 1.5、SD 2.1、SDXL
   - 微调模型：针对特定风格训练的模型
   - LoRA：轻量级的模型调整文件

2. **提示词(Prompt)**
   - 正向提示词：描述你想要的内容
   - 负向提示词：描述你不想要的内容

3. **采样器(Sampler)**
   - 控制图像生成的算法
   - 常用：DPM++ 2M Karras、Euler a

4. **步数(Steps)**
   - 生成图像的迭代次数
   - 通常20-50步即可

5. **CFG Scale**
   - 控制AI对提示词的遵循程度
   - 通常设置为7-12

## 第一次生成图片

### 基础操作流程

1. **启动WebUI**
   ```bash
   # Windows
   webui-user.bat
   
   # Linux/Mac
   ./webui.sh
   ```

2. **访问界面**
   - 打开浏览器访问 `http://localhost:7860`

3. **输入提示词**
   ```
   正向提示词示例：
   a beautiful girl, long hair, blue eyes, smile, portrait, high quality, detailed
   
   负向提示词示例：
   low quality, blurry, distorted, ugly, bad anatomy
   ```

4. **设置基础参数**
   - 采样器：DPM++ 2M Karras
   - 步数：20-30
   - CFG Scale：7-10
   - 尺寸：512x512 或 768x768

5. **点击生成**

### 第一个练习
尝试生成以下内容：
```
提示词：a cute cat sitting on a windowsill, sunlight, cozy room, detailed fur, photorealistic
负向提示词：blurry, low quality, distorted
```

## 提示词(Prompt)详解

### 提示词结构
```
[主体] + [描述] + [环境] + [风格] + [质量词]
```

### 常用提示词分类

#### 1. 人物相关
```
# 基础描述
girl, boy, woman, man, child, elderly

# 外貌特征
beautiful, handsome, cute, pretty
long hair, short hair, blonde hair, black hair
blue eyes, brown eyes, green eyes
smile, serious expression, happy

# 服装
dress, suit, casual clothes, uniform
red dress, blue shirt, white pants
```

#### 2. 风格相关
```
# 艺术风格
anime, realistic, cartoon, oil painting, watercolor
digital art, concept art, illustration

# 摄影风格
portrait, landscape, macro, street photography
studio lighting, natural lighting, golden hour
```

#### 3. 质量提升词
```
# 通用质量词
high quality, detailed, sharp, clear
masterpiece, best quality, ultra detailed
4k, 8k, high resolution

# 艺术质量词
trending on artstation, award winning
professional, studio quality
```

#### 4. 负向提示词
```
# 常用负向词
low quality, blurry, distorted, ugly
bad anatomy, bad hands, bad face
watermark, signature, text
duplicate, cropped, out of frame
```

### 提示词技巧

1. **权重调节**
   ```
   (keyword)     # 1.1倍权重
   ((keyword))   # 1.21倍权重
   (keyword:1.5) # 1.5倍权重
   [keyword]     # 0.9倍权重
   ```

2. **混合提示词**
   ```
   (cat:0.7) AND (dog:0.3)  # 70%猫 + 30%狗的特征
   ```

3. **步骤提示词**
   ```
   [cat:dog:0.5]  # 前50%步骤画猫，后50%步骤画狗
   ```

## 参数调节

### 重要参数详解

#### 1. 采样器(Sampler)
- **Euler a**：快速，适合简单图像
- **DPM++ 2M Karras**：质量好，推荐使用
- **DDIM**：稳定，适合批量生成

#### 2. 步数(Steps)
- 15-20步：快速预览
- 20-30步：日常使用
- 30-50步：高质量输出
- 50+步：通常没有明显改善

#### 3. CFG Scale
- 1-5：AI自由发挥，可能偏离提示词
- 7-12：平衡，推荐范围
- 15+：严格遵循提示词，可能过度饱和

#### 4. 尺寸设置
- **SD 1.5**：512x512, 512x768, 768x512
- **SDXL**：1024x1024, 1024x1536, 1536x1024

### 参数调节实践

尝试以下参数组合：
```
提示词：a serene mountain landscape at sunset
参数组合1：Steps=20, CFG=7, Sampler=DPM++ 2M Karras
参数组合2：Steps=30, CFG=10, Sampler=Euler a
参数组合3：Steps=25, CFG=8.5, Sampler=DDIM
```

观察不同参数对结果的影响。

## 模型选择

### 基础模型类型

#### 1. 官方基础模型
- **SD 1.5**：最经典，兼容性最好
- **SD 2.1**：质量提升，但兼容性较差
- **SDXL**：最新版本，质量最高

#### 2. 社区微调模型
- **写实风格**：Realistic Vision, ChilloutMix
- **动漫风格**：Anything V3/V4/V5, CounterfeitV3
- **艺术风格**：Deliberate, DreamShaper

### 模型下载与安装

1. **下载地址**
   - Hugging Face
   - Civitai（推荐）
   - GitHub

2. **安装位置**
   ```
   models/Stable-diffusion/     # 主模型
   models/Lora/                 # LoRA文件
   models/VAE/                  # VAE文件
   models/embeddings/           # Embedding文件
   ```

3. **模型推荐**
   ```
   新手推荐：
   - Deliberate（通用）
   - Realistic Vision（写实）
   - Anything V5（动漫）
   ```

## 进阶技巧

### 1. LoRA使用

LoRA是轻量级的模型调整文件，可以为基础模型添加特定风格或特征。

**使用方法：**
```
# 在提示词中添加
<lora:模型名称:权重>

# 示例
a beautiful girl <lora:korean_doll_likeness:0.8>
```

**权重建议：**
- 0.5-0.8：轻微影响
- 0.8-1.0：明显影响
- 1.0+：强烈影响（可能过度）

### 2. Controlnet使用

Controlnet可以精确控制图像的构图、姿势等。

**常用类型：**
- **Canny**：边缘检测，控制轮廓
- **Depth**：深度图，控制空间关系
- **Pose**：人体姿势控制
- **Scribble**：简笔画控制

**使用流程：**
1. 上传参考图片
2. 选择Controlnet类型
3. 调整权重（通常0.8-1.2）
4. 生成图片

### 3. 图生图(img2img)

基于现有图片进行修改和优化。

**重要参数：**
- **Denoising strength**：
  - 0.1-0.3：轻微修改
  - 0.4-0.7：中等修改
  - 0.8-1.0：大幅修改

**应用场景：**
- 照片转绘画风格
- 草图转精细图
- 图片风格转换
- 局部修改

### 4. 局部重绘(Inpainting)

精确修改图片的特定区域。

**操作步骤：**
1. 上传原图
2. 用画笔标记要修改的区域
3. 输入新的提示词
4. 调整参数生成

**技巧：**
- 遮罩边缘要平滑
- Denoising strength通常0.6-0.8
- 提示词要描述整个画面，不只是修改部分

### 5. 批量生成

**X/Y/Z Plot功能：**
可以批量测试不同参数的效果。

**常用测试组合：**
- X轴：不同CFG Scale值
- Y轴：不同采样器
- Z轴：不同步数

**批量提示词：**
```
# 使用 | 分隔不同提示词
a cat | a dog | a bird
```

## 常见问题解决

### 1. 生成质量问题

**问题：图片模糊不清**
- 解决：增加质量词，调高分辨率
- 提示词添加：high quality, detailed, sharp

**问题：人物面部畸形**
- 解决：使用face restoration功能
- 添加负向词：bad anatomy, bad face, distorted

**问题：手部变形**
- 解决：使用专门的手部LoRA
- 负向词：bad hands, extra fingers

### 2. 技术问题

**问题：显存不足**
- 解决方案：
  - 降低图片分辨率
  - 启用--medvram或--lowvram参数
  - 减少批量生成数量

**问题：生成速度慢**
- 解决方案：
  - 使用xformers加速
  - 减少采样步数
  - 选择更快的采样器

**问题：模型加载失败**
- 检查模型文件完整性
- 确认模型格式正确（.ckpt或.safetensors）
- 检查文件路径

### 3. 创作问题

**问题：无法生成想要的风格**
- 尝试不同的模型
- 调整提示词权重
- 使用相应的LoRA

**问题：构图不理想**
- 使用Controlnet控制构图
- 参考优秀作品的提示词
- 尝试不同的宽高比

## 学习资源

### 1. 官方资源
- **Stable Diffusion官网**：https://stability.ai/
- **Hugging Face**：https://huggingface.co/spaces/stabilityai/stable-diffusion
- **GitHub仓库**：https://github.com/AUTOMATIC1111/stable-diffusion-webui

### 2. 社区资源
- **Civitai**：模型分享平台，有大量优质模型和教程
- **Reddit r/StableDiffusion**：活跃的讨论社区
- **Discord社群**：实时交流和问题解答

### 3. 学习网站
- **OpenArt**：提示词分享和学习
- **PromptHero**：优质提示词收集
- **Lexica**：Stable Diffusion作品展示

### 4. 中文资源
- **B站教程**：搜索"Stable Diffusion教程"
- **知乎专栏**：有很多详细的技术文章
- **QQ群/微信群**：加入相关交流群

## 实践项目建议

### 初级项目
1. **头像生成器**：为自己生成不同风格的头像
2. **风景画创作**：生成各种风格的风景画
3. **动漫角色设计**：创作原创动漫角色

### 中级项目
1. **产品设计图**：为产品创意生成概念图
2. **室内设计**：生成不同风格的室内装修效果图
3. **服装设计**：设计各种风格的服装

### 高级项目
1. **连环画创作**：制作连贯的故事插图
2. **游戏美术资源**：为游戏创作角色和场景
3. **商业插画**：为商业用途创作专业插画

## 学习计划建议

### 第一周：基础入门
- 安装和配置环境
- 学习基本操作
- 掌握基础提示词
- 生成100张不同类型的图片

### 第二周：参数优化
- 深入理解各种参数
- 尝试不同的采样器和步数
- 学习负向提示词的使用
- 对比不同参数的效果

### 第三周：模型探索
- 下载和测试不同模型
- 学习LoRA的使用
- 尝试不同风格的创作
- 建立自己的模型库

### 第四周：进阶技巧
- 学习Controlnet使用
- 掌握图生图技巧
- 练习局部重绘
- 尝试复杂的工作流程

## 总结

Stable Diffusion是一个强大而灵活的AI绘画工具，掌握它需要理论学习和大量实践相结合。记住以下要点：

1. **从简单开始**：先掌握基础操作，再学习高级技巧
2. **多实践**：理论知识需要通过实践来巩固
3. **善用社区**：遇到问题时积极寻求社区帮助
4. **保持耐心**：AI绘画需要不断调试和优化
5. **持续学习**：技术在快速发展，要保持学习新功能

希望这份教程能帮助你快速入门Stable Diffusion，开启你的AI绘画之旅！

---

**提示**：这份教程涵盖了Stable Diffusion的主要内容，但AI绘画技术发展很快，建议定期关注最新的技术动态和社区资源。
