@echo off
chcp 65001 >nul
echo ========================================
echo ComfyUI 安装问题自动修复脚本
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ 检测到管理员权限
) else (
    echo ❌ 需要管理员权限！
    echo.
    echo 请右键点击此脚本，选择"以管理员身份运行"
    echo 或者：
    echo 1. 按 Win + X
    echo 2. 选择 "Windows PowerShell (管理员)"
    echo 3. 导航到此目录并运行脚本
    echo.
    pause
    exit /b 1
)

:: 检查当前目录
if not exist "main.py" (
    echo ❌ 未在ComfyUI目录中运行此脚本
    echo 请将此脚本放在ComfyUI根目录中运行
    pause
    exit /b 1
)

echo ✓ 在ComfyUI目录中运行
echo.

:: 显示修复选项
echo 请选择修复方案：
echo 1. 快速修复（推荐）- 清理缓存并重新安装
echo 2. 创建新虚拟环境
echo 3. 使用conda环境
echo 4. 仅修复权限问题
echo 5. 完全重新安装
echo.
set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" goto quick_fix
if "%choice%"=="2" goto new_venv
if "%choice%"=="3" goto conda_env
if "%choice%"=="4" goto fix_permissions
if "%choice%"=="5" goto full_reinstall

echo 无效选择，使用默认方案1
goto quick_fix

:quick_fix
echo.
echo ========================================
echo 执行快速修复...
echo ========================================

:: 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.10.6
    pause
    exit /b 1
)

echo ✓ Python已安装

:: 激活现有环境（如果存在）
if exist "venv\Scripts\activate.bat" (
    echo 激活现有虚拟环境...
    call venv\Scripts\activate.bat
) else if exist "comfyui_env\Scripts\activate.bat" (
    echo 激活现有虚拟环境...
    call comfyui_env\Scripts\activate.bat
) else (
    echo 未找到虚拟环境，在系统环境中执行...
)

:: 清理pip缓存
echo 清理pip缓存...
pip cache purge

:: 升级pip
echo 升级pip...
python -m pip install --upgrade pip

:: 修复权限
echo 修复Python目录权限...
for /f "tokens=*" %%i in ('python -c "import sys; print(sys.executable)"') do set PYTHON_PATH=%%i
for %%i in ("%PYTHON_PATH%") do set PYTHON_DIR=%%~dpi
icacls "%PYTHON_DIR%" /grant %USERNAME%:F /T >nul 2>&1

:: 重新安装依赖
echo 重新安装依赖包...
pip install -r requirements.txt --force-reinstall --no-cache-dir

goto verify_installation

:new_venv
echo.
echo ========================================
echo 创建新虚拟环境...
echo ========================================

:: 删除旧环境
if exist "venv" (
    echo 删除旧的虚拟环境...
    rmdir /s /q venv
)
if exist "comfyui_env" (
    echo 删除旧的虚拟环境...
    rmdir /s /q comfyui_env
)

:: 创建新环境
echo 创建新虚拟环境...
python -m venv comfyui_env
if errorlevel 1 (
    echo ❌ 创建虚拟环境失败
    pause
    exit /b 1
)

:: 激活环境
echo 激活虚拟环境...
call comfyui_env\Scripts\activate.bat

:: 升级pip
echo 升级pip...
python -m pip install --upgrade pip

:: 安装PyTorch
echo 安装PyTorch (CUDA版本)...
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
if errorlevel 1 (
    echo 警告: CUDA版本安装失败，尝试CPU版本...
    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
)

:: 安装其他依赖
echo 安装其他依赖...
pip install -r requirements.txt

goto verify_installation

:conda_env
echo.
echo ========================================
echo 使用conda环境...
echo ========================================

:: 检查conda
conda --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到conda，请先安装Miniconda或Anaconda
    echo 下载地址: https://docs.conda.io/en/latest/miniconda.html
    pause
    exit /b 1
)

echo ✓ conda已安装

:: 创建conda环境
echo 创建conda环境...
conda create -n comfyui python=3.10.6 -y

:: 激活环境
echo 激活conda环境...
call conda activate comfyui

:: 安装PyTorch
echo 安装PyTorch...
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y

:: 安装其他依赖
echo 安装其他依赖...
pip install -r requirements.txt

goto verify_installation

:fix_permissions
echo.
echo ========================================
echo 修复权限问题...
echo ========================================

:: 获取Python路径
for /f "tokens=*" %%i in ('python -c "import sys; print(sys.executable)"') do set PYTHON_PATH=%%i
for %%i in ("%PYTHON_PATH%") do set PYTHON_DIR=%%~dpi

echo Python目录: %PYTHON_DIR%

:: 修复权限
echo 修复权限...
icacls "%PYTHON_DIR%" /grant %USERNAME%:F /T
icacls "%PYTHON_DIR%Scripts" /grant %USERNAME%:F /T >nul 2>&1

:: 清理并重新安装pip
echo 重新安装pip...
python -m pip install --upgrade --force-reinstall pip

echo 权限修复完成
goto end

:full_reinstall
echo.
echo ========================================
echo 完全重新安装...
echo ========================================

:: 删除所有环境
if exist "venv" rmdir /s /q venv
if exist "comfyui_env" rmdir /s /q comfyui_env

:: 清理pip缓存
pip cache purge

:: 创建新环境
python -m venv comfyui_env
call comfyui_env\Scripts\activate.bat

:: 完整安装流程
python -m pip install --upgrade pip
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install -r requirements.txt

goto verify_installation

:verify_installation
echo.
echo ========================================
echo 验证安装...
echo ========================================

:: 测试Python导入
python -c "import torch; print(f'PyTorch版本: {torch.__version__}'); print(f'CUDA可用: {torch.cuda.is_available()}')" 2>nul
if errorlevel 1 (
    echo ❌ PyTorch安装验证失败
    goto installation_failed
)

echo ✓ PyTorch安装成功

:: 测试ComfyUI
python main.py --help >nul 2>&1
if errorlevel 1 (
    echo ❌ ComfyUI启动测试失败
    goto installation_failed
)

echo ✓ ComfyUI可以正常启动

goto installation_success

:installation_success
echo.
echo ========================================
echo 🎉 安装修复成功！
echo ========================================
echo.
echo 下一步操作：
echo 1. 下载基础模型到 models\checkpoints\ 目录
echo    推荐模型: v1-5-pruned-emaonly.ckpt
echo    下载地址: https://huggingface.co/runwayml/stable-diffusion-v1-5
echo.
echo 2. 启动ComfyUI:
if exist "comfyui_env\Scripts\activate.bat" (
    echo    comfyui_env\Scripts\activate.bat
    echo    python main.py
) else if exist "venv\Scripts\activate.bat" (
    echo    venv\Scripts\activate.bat  
    echo    python main.py
) else (
    echo    python main.py
)
echo.
echo 3. 访问界面: http://localhost:8188
echo.
echo 4. 可选启动参数:
echo    --listen 0.0.0.0  (允许外部访问)
echo    --port 8188       (指定端口)
echo    --lowvram         (低显存模式)
echo    --cpu             (CPU模式)
echo.

:: 创建启动脚本
echo 创建启动脚本...
if exist "comfyui_env\Scripts\activate.bat" (
    echo @echo off > start_comfyui.bat
    echo call comfyui_env\Scripts\activate.bat >> start_comfyui.bat
    echo python main.py %%* >> start_comfyui.bat
    echo pause >> start_comfyui.bat
    echo ✓ 已创建 start_comfyui.bat 启动脚本
)

goto end

:installation_failed
echo.
echo ========================================
echo ❌ 安装修复失败
echo ========================================
echo.
echo 可能的解决方案：
echo 1. 检查网络连接
echo 2. 确保有足够的磁盘空间
echo 3. 临时关闭防火墙和杀毒软件
echo 4. 尝试使用conda环境 (选项3)
echo 5. 手动安装依赖包
echo.
echo 如果问题持续，请查看详细错误信息或寻求技术支持
echo.

:end
pause
