# Stable Diffusion 实践练习指南

## 学习路径规划

### 第一阶段：基础入门（第1-2周）
- 熟悉界面操作
- 掌握基本提示词
- 理解核心参数
- 完成基础练习

### 第二阶段：技能提升（第3-4周）
- 学习高级提示词技巧
- 掌握不同模型使用
- 学习图生图功能
- 尝试风格控制

### 第三阶段：进阶应用（第5-8周）
- 掌握ControlNet使用
- 学习LoRA应用
- 练习复杂工作流
- 创作完整作品

## 第一周练习：基础操作

### 练习1：第一张图片
**目标**：熟悉基本界面和生成流程

**任务**：
```
提示词：a cute cat sitting on a table
负向提示词：blurry, low quality
参数：
- 采样器：DPM++ 2M Karras
- 步数：20
- CFG Scale：7
- 尺寸：512x512
```

**要求**：
- 生成5张不同的猫咪图片
- 观察每次生成的差异
- 记录种子值(Seed)的作用

### 练习2：提示词实验
**目标**：理解提示词对结果的影响

**任务A**：基础描述
```
1. a cat
2. a beautiful cat
3. a beautiful white cat
4. a beautiful white cat with blue eyes
5. a beautiful white cat with blue eyes, sitting in garden
```

**任务B**：风格对比
```
1. a girl, realistic
2. a girl, anime style
3. a girl, oil painting
4. a girl, watercolor
5. a girl, cartoon style
```

**要求**：
- 每个提示词生成3张图片
- 对比不同描述的效果
- 总结提示词规律

### 练习3：参数调节
**目标**：理解各种参数的作用

**固定提示词**：`a beautiful landscape with mountains and lake, sunset`

**实验参数**：
```
实验1 - CFG Scale：
- CFG 3, 7, 12, 15, 20

实验2 - 采样步数：
- Steps 10, 20, 30, 50

实验3 - 采样器：
- Euler a
- DPM++ 2M Karras
- DDIM
- LMS
```

**要求**：
- 记录每种参数的效果差异
- 找出最佳参数组合
- 理解参数与质量/速度的关系

### 练习4：负向提示词
**目标**：学会使用负向提示词改善图片质量

**基础提示词**：`portrait of a young woman`

**负向提示词实验**：
```
1. 无负向提示词
2. low quality, blurry
3. low quality, blurry, bad anatomy
4. low quality, blurry, bad anatomy, bad hands, bad face
5. low quality, blurry, bad anatomy, bad hands, bad face, distorted, ugly
```

**要求**：
- 对比不同负向提示词的效果
- 总结常用负向提示词
- 理解负向提示词的重要性

## 第二周练习：技能提升

### 练习5：人物肖像专项
**目标**：掌握人物生成技巧

**任务系列**：
```
A. 不同年龄：
- a cute child, 5 years old
- a beautiful teenager, 16 years old  
- a handsome young man, 25 years old
- an elegant middle-aged woman, 40 years old
- a wise elderly man, 70 years old

B. 不同风格：
- realistic portrait
- anime style portrait
- oil painting portrait
- pencil sketch portrait
- watercolor portrait

C. 不同表情：
- smiling happily
- serious expression
- surprised look
- peaceful meditation
- confident pose
```

### 练习6：场景构图
**目标**：学习场景描述和构图控制

**室内场景**：
```
1. cozy bedroom, warm lighting, books on nightstand
2. modern kitchen, clean design, morning sunlight
3. vintage library, old books, fireplace, armchair
4. art studio, paintings, easel, creative mess
5. cafe interior, wooden tables, plants, afternoon light
```

**室外场景**：
```
1. mountain landscape, snow peaks, clear sky, wide view
2. forest path, tall trees, dappled sunlight, mysterious
3. beach sunset, waves, golden hour, peaceful
4. city street, neon lights, night scene, urban
5. countryside, rolling hills, farmhouse, pastoral
```

### 练习7：风格迁移
**目标**：掌握不同艺术风格的应用

**固定主题**：`a tree in four seasons`

**风格变化**：
```
1. photorealistic style
2. impressionist painting style
3. japanese ink painting style
4. pixel art style
5. stained glass window style
6. art nouveau style
7. minimalist line art style
8. surrealist style
```

### 练习8：权重控制
**目标**：学习使用权重调节提示词影响力

**基础场景**：`a garden scene`

**权重实验**：
```
1. a garden with (flowers:1.2) and trees
2. a garden with (flowers:1.5) and trees
3. a garden with flowers and (trees:1.3)
4. a garden with (red flowers:1.4) and green trees
5. (beautiful garden:1.3) with flowers and trees
6. a garden with flowers and trees, (sunset lighting:1.2)
```

## 第三周练习：模型探索

### 练习9：不同模型对比
**目标**：理解不同模型的特点和适用场景

**测试模型**：
- SD 1.5 基础模型
- Deliberate
- Realistic Vision
- Anything V5
- DreamShaper

**统一测试提示词**：
```
1. portrait of a beautiful woman
2. fantasy landscape with castle
3. cute anime girl in school uniform
4. realistic photo of a cat
5. abstract art with vibrant colors
```

**要求**：
- 每个模型生成相同的5个主题
- 对比不同模型的风格特点
- 总结各模型的最佳应用场景

### 练习10：图生图入门
**目标**：学习基于现有图片进行创作

**准备材料**：
- 找一张简单的风景照片
- 找一张人物照片
- 画一个简单的草图

**实验任务**：
```
A. 风格转换：
- 照片 → 油画风格
- 照片 → 动漫风格
- 照片 → 水彩画风格

B. 草图转精细图：
- 简笔画 → 详细插画
- 线稿 → 上色作品

C. 局部修改：
- 改变背景
- 改变服装颜色
- 添加装饰元素
```

**参数设置**：
```
Denoising strength:
- 0.3：轻微修改
- 0.5：中等修改  
- 0.7：大幅修改
- 0.9：几乎重绘
```

## 第四周练习：进阶技巧

### 练习11：LoRA使用
**目标**：掌握LoRA的下载和使用

**推荐LoRA**：
- Korean Doll Likeness（韩系面孔）
- Detail Tweaker（细节增强）
- Clothing LoRA（服装风格）

**使用练习**：
```
1. 下载并安装LoRA
2. 基础使用：<lora:模型名:0.8>
3. 权重调节：测试0.5, 0.8, 1.0, 1.2的效果
4. 多LoRA组合使用
5. LoRA与不同基础模型的搭配
```

### 练习12：批量生成与对比
**目标**：学习批量测试和参数优化

**使用X/Y/Z Plot功能**：
```
实验1：
- X轴：CFG Scale (5, 7, 10, 12, 15)
- Y轴：Steps (15, 20, 25, 30)
- 固定提示词测试最佳参数组合

实验2：
- X轴：不同采样器
- Y轴：不同模型
- 对比效果差异

实验3：
- X轴：不同提示词变化
- Y轴：不同权重设置
```

### 练习13：复杂场景创作
**目标**：创作包含多个元素的复杂场景

**项目1：魔法学院**
```
提示词构建：
- 主体：young wizard student
- 环境：magical academy library
- 细节：floating books, glowing orbs, ancient scrolls
- 氛围：mystical lighting, magical particles
- 风格：fantasy art, detailed, cinematic
```

**项目2：未来城市**
```
提示词构建：
- 主体：cyberpunk character
- 环境：futuristic city street
- 细节：neon signs, flying cars, holographic displays
- 氛围：night scene, colorful lighting
- 风格：sci-fi, digital art, high tech
```

## 进阶项目练习

### 项目1：角色设计系列
**目标**：设计一套完整的角色

**要求**：
1. 设计3-5个不同职业的角色
2. 保持统一的艺术风格
3. 每个角色要有正面、侧面、背面视图
4. 设计角色的服装细节和道具

**角色设定**：
- 战士：重甲、武器、威严
- 法师：法袍、法杖、神秘
- 盗贼：轻装、敏捷、隐秘
- 牧师：圣袍、圣器、神圣
- 弓箭手：皮甲、弓箭、精准

### 项目2：四季风景系列
**目标**：创作同一地点的四季变化

**场景设定**：选择一个基础场景（如：湖边小屋）

**四季变化**：
```
春季：
- 嫩绿新叶、花朵盛开
- 温暖阳光、清新空气
- 生机勃勃的氛围

夏季：
- 浓绿树叶、炎热阳光
- 蓝天白云、湖水清澈
- 充满活力的感觉

秋季：
- 金黄红叶、收获季节
- 温暖色调、丰收氛围
- 宁静祥和的感觉

冬季：
- 白雪覆盖、冰冻湖面
- 冷色调、简洁构图
- 宁静肃穆的氛围
```

### 项目3：故事插画系列
**目标**：为一个简单故事创作插画

**故事大纲**：小红帽的现代版本
1. 现代都市中的小女孩
2. 穿过城市公园去看奶奶
3. 遇到"大灰狼"（可能是坏人或困难）
4. 机智解决问题
5. 安全到达奶奶家

**插画要求**：
- 5-8张关键场景插画
- 保持角色和风格一致性
- 体现故事的情感变化
- 使用合适的构图和色彩

## 学习评估

### 第一周评估标准
- [ ] 能够独立生成基础图片
- [ ] 理解基本参数的作用
- [ ] 掌握简单提示词使用
- [ ] 能够使用负向提示词改善质量

### 第二周评估标准
- [ ] 能够生成不同风格的图片
- [ ] 掌握人物和场景描述技巧
- [ ] 理解权重调节的作用
- [ ] 能够进行基础的风格控制

### 第三周评估标准
- [ ] 能够选择合适的模型
- [ ] 掌握图生图基本操作
- [ ] 理解不同模型的特点
- [ ] 能够进行风格转换

### 第四周评估标准
- [ ] 能够使用LoRA增强效果
- [ ] 掌握批量生成和对比
- [ ] 能够创作复杂场景
- [ ] 具备独立解决问题的能力

## 学习建议

### 每日练习计划
```
每天建议练习时间：1-2小时

第1-7天：基础练习，每天完成1-2个小练习
第8-14天：技能提升，每天尝试新的风格或技巧
第15-21天：模型探索，每天测试不同模型
第22-28天：进阶应用，开始创作完整作品
```

### 记录与总结
1. **建立练习日志**：记录每天的练习内容和心得
2. **保存优秀作品**：建立个人作品集
3. **记录有效提示词**：建立个人提示词库
4. **总结经验教训**：定期回顾和改进

### 社区参与
1. **加入相关社群**：与其他学习者交流
2. **分享作品**：获得反馈和建议
3. **学习他人作品**：分析优秀作品的技巧
4. **参与挑战**：参加社区的创作挑战

记住：AI绘画是一个需要大量实践的技能，坚持练习是成功的关键！
