@echo off
echo ========================================
echo ComfyUI 程序员快速安装脚本
echo ========================================
echo.

:: 检查Python版本
echo [1/8] 检查Python版本...
python --version 2>nul
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.10.6
    echo 下载地址: https://www.python.org/downloads/release/python-3106/
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo Python版本: %PYTHON_VERSION%

:: 检查是否为Python 3.10.x
echo %PYTHON_VERSION% | findstr "3.10" >nul
if errorlevel 1 (
    echo 警告: 推荐使用Python 3.10.6，当前版本可能不兼容
    echo 是否继续? (y/n)
    set /p choice=
    if /i not "%choice%"=="y" exit /b 1
)

:: 检查Git
echo [2/8] 检查Git...
git --version 2>nul
if errorlevel 1 (
    echo 错误: 未找到Git，请先安装Git
    echo 下载地址: https://git-scm.com/download/win
    pause
    exit /b 1
)

:: 克隆ComfyUI仓库
echo [3/8] 克隆ComfyUI仓库...
if exist ComfyUI (
    echo ComfyUI目录已存在，是否删除重新克隆? (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        rmdir /s /q ComfyUI
    ) else (
        echo 跳过克隆步骤
        goto :skip_clone
    )
)

git clone https://github.com/comfyanonymous/ComfyUI.git
if errorlevel 1 (
    echo 错误: 克隆失败，请检查网络连接
    pause
    exit /b 1
)

:skip_clone
cd ComfyUI

:: 创建虚拟环境
echo [4/8] 创建虚拟环境...
if exist venv (
    echo 虚拟环境已存在，是否重新创建? (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        rmdir /s /q venv
        python -m venv venv
    )
) else (
    python -m venv venv
)

:: 激活虚拟环境
echo [5/8] 激活虚拟环境...
call venv\Scripts\activate.bat

:: 升级pip
echo [6/8] 升级pip...
python -m pip install --upgrade pip

:: 安装PyTorch
echo [7/8] 安装PyTorch (CUDA版本)...
echo 正在安装PyTorch，这可能需要几分钟...
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
if errorlevel 1 (
    echo 警告: CUDA版本安装失败，尝试安装CPU版本...
    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
)

:: 安装其他依赖
echo [8/8] 安装其他依赖...
pip install -r requirements.txt

:: 创建模型目录
echo 创建模型目录...
mkdir models\checkpoints 2>nul
mkdir models\vae 2>nul
mkdir models\loras 2>nul
mkdir models\controlnet 2>nul
mkdir models\clip_vision 2>nul
mkdir models\embeddings 2>nul

:: 创建启动脚本
echo 创建启动脚本...
echo @echo off > start_comfyui.bat
echo call venv\Scripts\activate.bat >> start_comfyui.bat
echo python main.py %%* >> start_comfyui.bat
echo pause >> start_comfyui.bat

:: 创建配置文件
echo 创建配置文件...
echo { > config.json
echo   "models": { >> config.json
echo     "checkpoints_dir": "models/checkpoints", >> config.json
echo     "vae_dir": "models/vae", >> config.json
echo     "loras_dir": "models/loras", >> config.json
echo     "controlnet_dir": "models/controlnet" >> config.json
echo   }, >> config.json
echo   "generation": { >> config.json
echo     "default_steps": 20, >> config.json
echo     "default_cfg": 7.0, >> config.json
echo     "default_size": [512, 512] >> config.json
echo   } >> config.json
echo } >> config.json

echo.
echo ========================================
echo 安装完成！
echo ========================================
echo.
echo 下一步操作：
echo 1. 下载基础模型到 models/checkpoints/ 目录
echo    推荐: SD 1.5 (v1-5-pruned-emaonly.ckpt)
echo    下载地址: https://huggingface.co/runwayml/stable-diffusion-v1-5
echo.
echo 2. 启动ComfyUI:
echo    双击 start_comfyui.bat
echo    或在命令行运行: python main.py
echo.
echo 3. 访问界面:
echo    http://localhost:8188
echo.
echo 4. 可选参数:
echo    --listen 0.0.0.0  (允许外部访问)
echo    --port 8188       (指定端口)
echo    --lowvram         (低显存模式)
echo    --cpu             (CPU模式)
echo.
echo 程序员专用提示：
echo - 工作流保存为JSON格式，可以版本控制
echo - 支持HTTP API和WebSocket API
echo - 可以开发自定义节点扩展功能
echo - 查看教程: ComfyUI程序员入门教程.md
echo.
pause
