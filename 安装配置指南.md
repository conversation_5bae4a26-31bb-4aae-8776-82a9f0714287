# Stable Diffusion 安装配置指南

## 系统要求检查

### 硬件要求
```
最低配置：
- GPU: NVIDIA GTX 1060 6GB 或 AMD RX 580 8GB
- CPU: Intel i5-8400 或 AMD Ryzen 5 2600
- 内存: 8GB RAM
- 存储: 20GB 可用空间
- 系统: Windows 10/11, Linux, macOS

推荐配置：
- GPU: NVIDIA RTX 3060 12GB 或更高
- CPU: Intel i7-10700 或 AMD Ryzen 7 3700X
- 内存: 16GB RAM
- 存储: 50GB+ SSD空间
- 系统: Windows 11, Ubuntu 20.04+
```

### 检查GPU支持
```bash
# Windows - 检查GPU信息
nvidia-smi

# 检查CUDA版本
nvcc --version

# 如果没有CUDA，需要安装CUDA Toolkit
```

## 方法一：AUTOMATIC1111 WebUI 安装（推荐）

### Windows 安装步骤

#### 1. 安装前置软件
```bash
# 1. 下载并安装 Python 3.10.6
# 官网：https://www.python.org/downloads/release/python-3106/
# 注意：勾选 "Add Python to PATH"

# 2. 下载并安装 Git
# 官网：https://git-scm.com/download/win

# 3. 验证安装
python --version  # 应该显示 Python 3.10.6
git --version     # 应该显示 git 版本
```

#### 2. 下载 WebUI
```bash
# 创建工作目录
mkdir C:\stable-diffusion
cd C:\stable-diffusion

# 克隆仓库
git clone https://github.com/AUTOMATIC1111/stable-diffusion-webui.git
cd stable-diffusion-webui
```

#### 3. 首次运行
```bash
# 运行安装脚本（会自动下载依赖）
webui-user.bat

# 首次运行会下载基础模型，需要等待较长时间
# 完成后会显示：Running on local URL: http://127.0.0.1:7860
```

#### 4. 访问界面
- 打开浏览器访问：`http://localhost:7860`
- 如果看到Stable Diffusion界面，说明安装成功！

### Linux 安装步骤

#### Ubuntu/Debian
```bash
# 1. 更新系统
sudo apt update && sudo apt upgrade -y

# 2. 安装依赖
sudo apt install wget git python3 python3-venv python3-pip -y

# 3. 安装CUDA（如果有NVIDIA GPU）
# 访问：https://developer.nvidia.com/cuda-downloads
# 选择对应版本下载安装

# 4. 克隆仓库
git clone https://github.com/AUTOMATIC1111/stable-diffusion-webui.git
cd stable-diffusion-webui

# 5. 运行
./webui.sh
```

#### CentOS/RHEL
```bash
# 1. 安装依赖
sudo yum install git python3 python3-pip -y

# 2. 其他步骤同Ubuntu
```

### macOS 安装步骤

#### 使用 Homebrew
```bash
# 1. 安装 Homebrew（如果没有）
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 2. 安装依赖
brew install cmake protobuf rust python@3.10 git wget

# 3. 克隆仓库
git clone https://github.com/AUTOMATIC1111/stable-diffusion-webui.git
cd stable-diffusion-webui

# 4. 运行
./webui.sh
```

## 方法二：ComfyUI 安装

### Windows 安装
```bash
# 1. 克隆仓库
git clone https://github.com/comfyanonymous/ComfyUI.git
cd ComfyUI

# 2. 安装依赖
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install -r requirements.txt

# 3. 运行
python main.py

# 访问：http://localhost:8188
```

## 配置优化

### 启动参数优化

#### webui-user.bat 配置（Windows）
```batch
@echo off

set PYTHON=
set GIT=
set VENV_DIR=
set COMMANDLINE_ARGS=--xformers --enable-insecure-extension-access --api

call webui.bat
```

#### 常用启动参数
```bash
# 性能优化
--xformers              # 启用xformers加速
--opt-sdp-attention     # 优化注意力机制
--opt-sub-quad-attention # 进一步优化注意力

# 内存优化
--medvram              # 中等显存优化
--lowvram              # 低显存优化
--opt-split-attention  # 分割注意力计算

# 功能扩展
--api                  # 启用API
--listen               # 允许外部访问
--port 7860           # 指定端口
--enable-insecure-extension-access  # 允许不安全扩展
```

### 目录结构说明
```
stable-diffusion-webui/
├── models/
│   ├── Stable-diffusion/    # 主模型文件 (.ckpt, .safetensors)
│   ├── Lora/               # LoRA文件
│   ├── VAE/                # VAE文件
│   ├── embeddings/         # Textual Inversion文件
│   ├── hypernetworks/      # Hypernetwork文件
│   └── ControlNet/         # ControlNet模型
├── extensions/             # 扩展插件
├── outputs/               # 生成的图片
└── scripts/               # 脚本文件
```

## 模型下载与安装

### 基础模型下载

#### 官方模型
```bash
# SD 1.5 (推荐新手)
https://huggingface.co/runwayml/stable-diffusion-v1-5

# SDXL (最新版本)
https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0
```

#### 社区模型（Civitai）
```
推荐模型：
1. Deliberate - 通用模型，质量高
2. Realistic Vision - 写实风格
3. Anything V5 - 动漫风格
4. DreamShaper - 艺术风格
5. ChilloutMix - 亚洲面孔写实
```

### 模型安装步骤
```bash
# 1. 下载模型文件（.ckpt 或 .safetensors）
# 2. 放入对应目录
cp model.safetensors stable-diffusion-webui/models/Stable-diffusion/

# 3. 重启WebUI或点击刷新按钮
# 4. 在界面左上角选择新模型
```

## 扩展插件安装

### 推荐扩展

#### 1. ControlNet
```
功能：精确控制图像生成
安装：Extensions -> Available -> 搜索 "controlnet" -> Install
```

#### 2. Deforum
```
功能：视频生成
安装：https://github.com/deforum-art/deforum-for-automatic1111-webui
```

#### 3. Additional Networks
```
功能：更好的LoRA支持
安装：Extensions -> Available -> 搜索 "additional networks"
```

#### 4. Image Browser
```
功能：图片浏览管理
安装：Extensions -> Available -> 搜索 "image browser"
```

### 扩展安装方法
```bash
# 方法1：通过界面安装
Extensions -> Available -> 搜索 -> Install -> Restart

# 方法2：手动安装
cd stable-diffusion-webui/extensions
git clone [扩展仓库地址]
# 重启WebUI
```

## 常见问题解决

### 安装问题

#### Python版本问题
```bash
# 错误：Python版本不兼容
# 解决：确保使用Python 3.10.6
python --version

# 如果版本不对，重新安装Python 3.10.6
```

#### Git问题
```bash
# 错误：git不是内部或外部命令
# 解决：安装Git并添加到PATH

# 验证Git安装
git --version
```

#### 网络问题
```bash
# 错误：下载失败或速度慢
# 解决：使用镜像源或VPN

# 设置pip镜像
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
```

### 运行问题

#### 显存不足
```bash
# 错误：CUDA out of memory
# 解决：添加启动参数
--medvram    # 中等显存
--lowvram    # 低显存
--opt-split-attention  # 优化注意力计算
```

#### 模型加载失败
```bash
# 错误：模型无法加载
# 解决：
1. 检查模型文件完整性
2. 确认文件格式（.ckpt或.safetensors）
3. 检查文件路径
4. 重新下载模型
```

#### 生成速度慢
```bash
# 解决方案：
1. 添加 --xformers 参数
2. 降低图片分辨率
3. 减少采样步数
4. 使用更快的采样器
```

## 性能优化建议

### 硬件优化
```
1. 使用SSD存储模型文件
2. 确保足够的系统内存
3. 保持GPU驱动最新
4. 监控GPU温度，避免过热降频
```

### 软件优化
```
1. 定期清理输出文件夹
2. 关闭不必要的后台程序
3. 使用合适的启动参数
4. 定期更新WebUI版本
```

### 使用技巧
```
1. 批量生成时使用较低分辨率
2. 精细调整时使用高分辨率
3. 合理设置采样步数（20-30步通常足够）
4. 使用VAE提升图片质量
```

## 备份与迁移

### 重要文件备份
```bash
# 需要备份的文件/文件夹：
models/                 # 所有模型文件
extensions/            # 扩展插件
config.json           # 配置文件
ui-config.json        # 界面配置
webui-user.bat        # 启动配置（Windows）
```

### 迁移步骤
```bash
# 1. 备份重要文件
# 2. 在新系统安装WebUI
# 3. 复制备份文件到对应位置
# 4. 重启WebUI
```

安装完成后，建议先阅读主教程文档，然后开始你的AI绘画之旅！
