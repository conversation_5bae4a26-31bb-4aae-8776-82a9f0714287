# ComfyUI 程序员入门教程

## 前言：为什么程序员适合ComfyUI

作为程序员，你会发现ComfyUI的节点式编程思维与你熟悉的概念非常相似：
- **节点** = 函数/方法
- **连接线** = 数据流/管道
- **工作流** = 程序逻辑
- **参数** = 函数参数
- **输出** = 返回值

## 环境准备

### 系统要求
```
推荐配置：
- GPU: NVIDIA RTX 3060+ (8GB+ VRAM)
- CPU: 多核处理器
- RAM: 16GB+
- 存储: 50GB+ SSD空间
- Python: 3.10.6 (重要！)
```

### 安装步骤

#### 1. 克隆仓库
```bash
git clone https://github.com/comfyanonymous/ComfyUI.git
cd ComfyUI
```

#### 2. 创建虚拟环境（推荐）
```bash
# 使用conda
conda create -n comfyui python=3.10.6 -y
conda activate comfyui

# 或使用venv
python -m venv comfyui_env
# Windows
comfyui_env\Scripts\activate
# Linux/Mac
source comfyui_env/bin/activate
```

#### 3. 安装依赖
```bash
# NVIDIA GPU (CUDA)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# AMD GPU (ROCm) - Linux
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/rocm5.4.2

# CPU only (不推荐)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu

# 安装其他依赖
pip install -r requirements.txt
```

#### 4. 下载基础模型
```bash
# 创建模型目录
mkdir -p models/checkpoints

# 下载SD 1.5基础模型（约4GB）
# 方法1：直接下载
wget -O models/checkpoints/v1-5-pruned-emaonly.ckpt \
  https://huggingface.co/runwayml/stable-diffusion-v1-5/resolve/main/v1-5-pruned-emaonly.ckpt

# 方法2：使用git lfs
git lfs install
git clone https://huggingface.co/runwayml/stable-diffusion-v1-5 models/checkpoints/sd15
```

#### 5. 启动ComfyUI
```bash
python main.py

# 可选参数
python main.py --listen 0.0.0.0 --port 8188  # 允许外部访问
python main.py --cpu                          # 强制使用CPU
python main.py --lowvram                      # 低显存模式
```

访问：`http://localhost:8188`

## 核心概念理解

### 1. 节点系统架构
```
ComfyUI采用数据流编程模型：

Input Nodes → Processing Nodes → Output Nodes
     ↓              ↓              ↓
  数据源         数据处理        结果输出
```

### 2. 基础节点类型

#### 输入节点 (Input Nodes)
```python
# 类比：数据源/配置
- Load Checkpoint: 加载模型 (类似import模块)
- CLIP Text Encode: 文本编码器 (类似字符串处理)
- Load Image: 图片加载器 (类似文件读取)
- Empty Latent Image: 创建空白画布 (类似初始化数组)
```

#### 处理节点 (Processing Nodes)
```python
# 类比：业务逻辑函数
- KSampler: 采样器 (核心算法，类似主要业务逻辑)
- VAE Decode: 解码器 (类似数据转换函数)
- ControlNet Apply: 控制网络 (类似条件判断)
```

#### 输出节点 (Output Nodes)
```python
# 类比：结果输出
- Save Image: 保存图片 (类似文件写入)
- Preview Image: 预览图片 (类似console.log/print)
```

### 3. 数据类型系统
```python
# ComfyUI的数据类型（类似强类型语言）
MODEL      # 模型对象
CLIP       # 文本编码器
VAE        # 变分自编码器
CONDITIONING  # 条件信息（正向/负向提示词）
LATENT     # 潜在空间数据
IMAGE      # 图像数据
MASK       # 遮罩数据
INT        # 整数
FLOAT      # 浮点数
STRING     # 字符串
```

## 第一个工作流：Hello World

### 基础文生图工作流
```
这个工作流相当于以下伪代码：

model = load_checkpoint("sd15.ckpt")
positive_prompt = encode_text("a beautiful cat")
negative_prompt = encode_text("blurry, low quality")
latent = create_empty_latent(512, 512)
result = sample(model, positive_prompt, negative_prompt, latent)
image = vae_decode(result)
save_image(image)
```

### 节点连接步骤

#### 1. 添加基础节点
```
右键 → Add Node → 选择节点类型

必需节点：
- Load Checkpoint
- CLIP Text Encode (Prompt) × 2
- Empty Latent Image  
- KSampler
- VAE Decode
- Save Image
```

#### 2. 配置节点参数
```python
# Load Checkpoint
ckpt_name: "v1-5-pruned-emaonly.ckpt"

# CLIP Text Encode (正向)
text: "a beautiful cat, high quality, detailed"

# CLIP Text Encode (负向)  
text: "blurry, low quality, bad anatomy"

# Empty Latent Image
width: 512
height: 512
batch_size: 1

# KSampler
seed: 42 (或-1随机)
steps: 20
cfg: 7.0
sampler_name: "dpmpp_2m"
scheduler: "karras"
denoise: 1.0
```

#### 3. 连接节点
```
连接逻辑（类似函数调用链）：

Load Checkpoint → MODEL → KSampler
Load Checkpoint → CLIP → CLIP Text Encode (正向)
Load Checkpoint → CLIP → CLIP Text Encode (负向)
Load Checkpoint → VAE → VAE Decode

CLIP Text Encode (正向) → CONDITIONING → KSampler (positive)
CLIP Text Encode (负向) → CONDITIONING → KSampler (negative)
Empty Latent Image → LATENT → KSampler (latent_image)

KSampler → LATENT → VAE Decode
VAE Decode → IMAGE → Save Image
```

#### 4. 执行工作流
```
点击 "Queue Prompt" 按钮
等待执行完成
查看生成的图片
```

## 进阶概念

### 1. 批量处理
```python
# 类似循环处理
for i in range(batch_count):
    for j in range(batch_size):
        generate_image(seed + i * batch_size + j)
```

### 2. 条件分支
```python
# 使用Switch节点实现条件逻辑
if condition:
    use_model_A()
else:
    use_model_B()
```

### 3. 数据流控制
```python
# 类似管道操作
image_pipeline = (
    load_image()
    .resize(512, 512)
    .apply_controlnet()
    .generate()
    .post_process()
    .save()
)
```

## 常用工作流模板

### 1. 图生图工作流
```
相当于：
input_image = load_image("input.jpg")
latent = vae_encode(input_image)
result = sample(model, prompt, latent, denoise=0.7)
output = vae_decode(result)
```

### 2. ControlNet工作流
```
相当于：
control_image = preprocess_image(input_image, "canny")
controlnet = load_controlnet("canny")
result = sample_with_control(model, prompt, control_image, controlnet)
```

### 3. LoRA工作流
```
相当于：
base_model = load_checkpoint("base.ckpt")
lora_model = apply_lora(base_model, "style.safetensors", strength=0.8)
result = sample(lora_model, prompt)
```

## 自定义节点开发

### 节点结构
```python
class MyCustomNode:
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "input_param": ("STRING", {"default": "default_value"}),
                "numeric_param": ("FLOAT", {"default": 1.0, "min": 0.0, "max": 2.0}),
            }
        }
    
    RETURN_TYPES = ("IMAGE",)
    FUNCTION = "process"
    CATEGORY = "custom"
    
    def process(self, input_param, numeric_param):
        # 处理逻辑
        result = your_processing_function(input_param, numeric_param)
        return (result,)

# 注册节点
NODE_CLASS_MAPPINGS = {
    "MyCustomNode": MyCustomNode
}
```

## 性能优化技巧

### 1. 内存管理
```python
# 启动参数优化
python main.py --lowvram          # 低显存模式
python main.py --normalvram       # 正常显存模式
python main.py --highvram         # 高显存模式
python main.py --cpu              # CPU模式
```

### 2. 批量优化
```python
# 使用批量处理而不是循环
batch_size = 4  # 根据显存调整
# 而不是单张循环处理
```

### 3. 模型管理
```python
# 模型缓存策略
- 频繁使用的模型保持在内存中
- 不常用的模型及时释放
- 使用模型切换节点优化加载
```

## 调试技巧

### 1. 节点调试
```python
# 使用Preview Image节点查看中间结果
# 类似在代码中添加print语句
intermediate_result → Preview Image
```

### 2. 参数调试
```python
# 使用Primitive节点创建可调参数
# 类似配置文件或环境变量
Primitive (INT) → KSampler (steps)
```

### 3. 工作流版本控制
```json
// 保存工作流为JSON文件
// 类似代码版本管理
{
  "workflow_version": "1.0",
  "nodes": [...],
  "links": [...]
}
```

## API使用

### 1. HTTP API
```python
import requests
import json

# 提交工作流
workflow = {...}  # 工作流JSON
response = requests.post(
    "http://localhost:8188/prompt",
    json={"prompt": workflow}
)

# 获取结果
prompt_id = response.json()["prompt_id"]
# 轮询或使用WebSocket获取结果
```

### 2. WebSocket API
```python
import websocket
import json

def on_message(ws, message):
    data = json.loads(message)
    if data["type"] == "executed":
        print("节点执行完成:", data["data"]["node"])

ws = websocket.WebSocketApp(
    "ws://localhost:8188/ws",
    on_message=on_message
)
ws.run_forever()
```

## 常见问题解决

### 1. 内存不足
```python
# 解决方案
- 减少batch_size
- 使用--lowvram参数
- 降低图片分辨率
- 关闭不必要的程序
```

### 2. 节点连接错误
```python
# 检查数据类型匹配
# 类似类型检查错误
MODEL → MODEL  ✓
IMAGE → MODEL  ✗
```

### 3. 模型加载失败
```python
# 检查文件路径和格式
models/checkpoints/model.ckpt     ✓
models/checkpoints/model.safetensors  ✓
models/checkpoints/model.bin      ✗
```

## 实战项目

### 项目1：自动化头像生成器
```python
# 目标：批量生成不同风格的头像
# 技术栈：ComfyUI + Python脚本

工作流设计：
1. 输入：基础人物描述
2. 处理：应用不同风格LoRA
3. 输出：多种风格头像

实现思路：
- 使用LoRA Loader节点
- 创建风格切换逻辑
- 批量队列处理
```

### 项目2：图片风格转换API
```python
# 目标：提供RESTful API服务
# 技术栈：ComfyUI + FastAPI

from fastapi import FastAPI, UploadFile
import requests

app = FastAPI()

@app.post("/style-transfer")
async def style_transfer(image: UploadFile, style: str):
    # 1. 保存上传的图片
    # 2. 构建ComfyUI工作流
    # 3. 提交到ComfyUI API
    # 4. 返回处理结果
    pass
```

### 项目3：智能工作流生成器
```python
# 目标：根据需求自动生成工作流
# 技术栈：ComfyUI + 规则引擎

class WorkflowGenerator:
    def __init__(self):
        self.templates = {
            "txt2img": self.basic_txt2img,
            "img2img": self.basic_img2img,
            "controlnet": self.controlnet_workflow
        }

    def generate(self, task_type, params):
        template = self.templates[task_type]
        return template(params)
```

## 高级技巧

### 1. 工作流模块化
```python
# 创建可复用的工作流组件
# 类似函数封装

# 基础组件
def create_text_encoder_group():
    return {
        "positive_encoder": "CLIPTextEncode",
        "negative_encoder": "CLIPTextEncode"
    }

# 组合使用
workflow = {
    **create_text_encoder_group(),
    **create_sampler_group(),
    **create_output_group()
}
```

### 2. 参数化配置
```python
# 使用配置文件管理参数
# config.json
{
    "models": {
        "base": "v1-5-pruned-emaonly.ckpt",
        "lora_dir": "models/loras/"
    },
    "generation": {
        "default_steps": 20,
        "default_cfg": 7.0,
        "default_size": [512, 512]
    }
}

# 在工作流中使用
import json
config = json.load(open("config.json"))
```

### 3. 错误处理和重试
```python
# 实现健壮的工作流执行
import time
import requests

def execute_workflow_with_retry(workflow, max_retries=3):
    for attempt in range(max_retries):
        try:
            response = requests.post(
                "http://localhost:8188/prompt",
                json={"prompt": workflow},
                timeout=30
            )
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            print(f"Attempt {attempt + 1} failed: {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # 指数退避
    raise Exception("All retry attempts failed")
```

## 扩展开发

### 1. 自定义采样器
```python
# 实现自定义采样算法
class CustomSampler:
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "model": ("MODEL",),
                "positive": ("CONDITIONING",),
                "negative": ("CONDITIONING",),
                "latent_image": ("LATENT",),
                "seed": ("INT", {"default": 0}),
                "steps": ("INT", {"default": 20}),
                "cfg": ("FLOAT", {"default": 8.0}),
                "custom_param": ("FLOAT", {"default": 1.0})
            }
        }

    RETURN_TYPES = ("LATENT",)
    FUNCTION = "sample"
    CATEGORY = "sampling"

    def sample(self, model, positive, negative, latent_image,
               seed, steps, cfg, custom_param):
        # 实现自定义采样逻辑
        # 可以调用现有的采样器并添加自定义处理
        pass
```

### 2. 数据处理节点
```python
# 图像预处理节点
class ImagePreprocessor:
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "image": ("IMAGE",),
                "operation": (["resize", "crop", "rotate"],),
                "value": ("FLOAT", {"default": 1.0})
            }
        }

    RETURN_TYPES = ("IMAGE",)
    FUNCTION = "process"
    CATEGORY = "image/preprocessing"

    def process(self, image, operation, value):
        # 实现图像处理逻辑
        import torch
        import torchvision.transforms as T

        if operation == "resize":
            transform = T.Resize(int(value))
        elif operation == "crop":
            transform = T.CenterCrop(int(value))
        # ... 其他操作

        processed = transform(image)
        return (processed,)
```

## 部署和生产环境

### 1. Docker部署
```dockerfile
# Dockerfile
FROM nvidia/cuda:11.8-devel-ubuntu20.04

RUN apt-get update && apt-get install -y \
    python3.10 \
    python3-pip \
    git

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8188

CMD ["python", "main.py", "--listen", "0.0.0.0"]
```

### 2. 负载均衡
```python
# 多实例负载均衡
import random
import requests

class ComfyUICluster:
    def __init__(self, instances):
        self.instances = instances  # ["http://node1:8188", ...]

    def submit_workflow(self, workflow):
        # 简单轮询或随机选择
        instance = random.choice(self.instances)
        return requests.post(f"{instance}/prompt", json=workflow)
```

### 3. 监控和日志
```python
# 性能监控
import psutil
import logging

class ComfyUIMonitor:
    def __init__(self):
        self.logger = logging.getLogger("comfyui_monitor")

    def log_system_stats(self):
        cpu_percent = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        gpu_memory = self.get_gpu_memory()

        self.logger.info(f"CPU: {cpu_percent}%, "
                        f"RAM: {memory.percent}%, "
                        f"GPU: {gpu_memory}%")
```

## 最佳实践

### 1. 代码组织
```
comfyui_project/
├── workflows/          # 工作流模板
│   ├── basic/
│   ├── advanced/
│   └── custom/
├── nodes/             # 自定义节点
│   ├── __init__.py
│   ├── samplers.py
│   └── processors.py
├── api/               # API封装
│   ├── client.py
│   └── server.py
├── config/            # 配置文件
│   ├── models.json
│   └── settings.json
├── tests/             # 测试用例
└── requirements.txt
```

### 2. 版本控制
```bash
# .gitignore
models/
outputs/
temp/
__pycache__/
*.pyc
.env

# 只提交工作流和代码
git add workflows/ nodes/ api/ config/
```

### 3. 文档规范
```python
# 节点文档示例
class WellDocumentedNode:
    """
    自定义图像处理节点

    功能：对输入图像应用指定的变换

    输入：
        image: 输入图像 (IMAGE类型)
        transform: 变换类型 (STRING类型)
        strength: 变换强度 (FLOAT类型, 0.0-2.0)

    输出：
        image: 处理后的图像 (IMAGE类型)

    示例：
        input_image → WellDocumentedNode → output_image
    """
    pass
```

## 学习路径建议

### 第1周：基础掌握
- [ ] 完成环境搭建
- [ ] 理解节点系统概念
- [ ] 创建第一个工作流
- [ ] 掌握基础节点使用

### 第2周：进阶应用
- [ ] 学习图生图工作流
- [ ] 掌握ControlNet使用
- [ ] 了解LoRA应用
- [ ] 尝试批量处理

### 第3周：自定义开发
- [ ] 开发第一个自定义节点
- [ ] 学习API使用
- [ ] 实现简单的自动化脚本
- [ ] 优化工作流性能

### 第4周：生产应用
- [ ] 设计完整的项目
- [ ] 实现错误处理和监控
- [ ] 考虑部署方案
- [ ] 编写文档和测试

## 资源推荐

### 官方资源
- GitHub: https://github.com/comfyanonymous/ComfyUI
- 示例工作流: https://comfyanonymous.github.io/ComfyUI_examples/

### 社区资源
- ComfyUI Manager: 节点管理器
- Custom Nodes: 社区自定义节点
- Workflow Gallery: 工作流分享

### 开发工具
- ComfyUI API文档生成器
- 工作流可视化工具
- 性能分析工具

作为程序员，你会发现ComfyUI的学习曲线虽然陡峭，但概念清晰，逻辑性强。建议从基础工作流开始，逐步深入到自定义开发。
